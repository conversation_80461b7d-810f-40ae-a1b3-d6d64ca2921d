2025-07-11 19:13:52,077 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-11 19:13:52,078 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-11 19:13:52,079 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-11 19:13:52,079 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-11 19:13:52,079 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-11 19:13:52,080 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-11 19:13:52,080 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-11 19:13:52,081 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-11 19:13:52,081 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-11 19:13:52,082 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-11 19:13:52,082 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-11 19:13:52,082 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-11 19:13:52,082 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-11 19:13:54,168 - __main__ - INFO - Existing processes terminated
2025-07-11 19:13:55,994 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-07-11 19:13:55,995 - utils.global_values_db - INFO - Global values database initialized successfully
2025-07-11 19:13:55,995 - utils.global_values_db - INFO - Using global values from config.py
2025-07-11 19:13:55,995 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-07-11 19:13:55,998 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-07-11 19:13:55,998 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-07-11 19:13:56,047 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-07-11 19:13:56,731 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-07-11 19:13:56,732 - utils.database - INFO - Test_steps table schema updated successfully
2025-07-11 19:13:56,732 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-07-11 19:13:56,732 - utils.database - INFO - Screenshots table schema updated successfully
2025-07-11 19:13:56,732 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-07-11 19:13:56,733 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-07-11 19:13:56,733 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-07-11 19:13:56,733 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-07-11 19:13:56,733 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-07-11 19:13:56,733 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-07-11 19:13:56,734 - utils.database - INFO - Database initialized successfully
2025-07-11 19:13:56,734 - utils.database - INFO - Checking initial database state...
2025-07-11 19:13:56,764 - utils.database - INFO - Database state: 0 suites, 0 cases, 11198 steps, 1 screenshots, 4 tracking entries
2025-07-11 19:13:56,782 - app - INFO - Using directories from config.py:
2025-07-11 19:13:56,782 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-11 19:13:56,782 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-11 19:13:56,782 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-11 19:13:56,916] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-11 19:13:56,929] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11be3aa50>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-11 19:13:56,929] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-07-11 19:13:56,974] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-07-11 19:13:57,023] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-07-11 19:13:59,029] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-07-11 19:13:59,030] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-07-11 19:14:00,712] INFO in appium_device_controller: Installed Appium drivers: 
[2025-07-11 19:14:00,713] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-07-11 19:14:01,507] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-07-11 19:14:01,507] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-07-11 19:14:01,507] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-07-11 19:14:01,530] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-07-11 19:14:03,538] WARNING in appium_device_controller: Waiting for Appium server to start (attempt 1/15): HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x11bf51310>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-07-11 19:14:05,558] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:05,559] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:07,565] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:07,565] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:09,571] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:09,571] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:11,579] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:11,579] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:13,585] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:13,585] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:15,590] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:15,590] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:17,598] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:17,598] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:19,608] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:19,608] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:21,616] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:21,616] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:23,627] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:23,627] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:25,638] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:25,638] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:27,646] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:27,646] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:29,652] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:29,652] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:14:31,663] INFO in appium_device_controller: Appium server started successfully
[2025-07-11 19:14:31,663] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8100
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-07-11 19:14:31,737] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-07-11 19:14:31,737] INFO in _internal: [33mPress CTRL+C to quit[0m
[2025-07-11 19:14:31,762] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-11 19:14:31,762] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:14:31,764] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:14:31] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:14:36,758] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:14:36,759] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:14:36] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:14:41,758] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:14:41,759] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:14:41] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:14:43,319] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:14:43] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-11 19:14:46,757] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:14:46,758] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:14:46] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:14:51,757] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:14:51,757] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:14:51] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:14:56,760] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:14:56,761] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:14:56] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:01,759] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:01,760] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:01] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:06,760] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:06,761] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:06] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:09,731] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET / HTTP/1.1" 200 -
[2025-07-11 19:15:09,765] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/test-cases-styles.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,765] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/modern-styles.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,765] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,766] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/test-suites-styles.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,774] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/fixed-device-screen.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,774] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/execution-overlay.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,775] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/actionStyles.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,777] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/test-case-modification.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,779] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/css/test-case.css HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,781] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/modules/uiUtils.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,783] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/modules/reportAndFormUtils.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,784] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /static/js/modules/actionFormManager.js HTTP/1.1" 200 -
[2025-07-11 19:15:09,786] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/export-run.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,787] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/img/no_device.png HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,788] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/utils.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,794] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /static/js/action-manager.js?v=1752228309 HTTP/1.1" 200 -
[2025-07-11 19:15:09,797] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/modules/ElementInteractions.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,804] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/fixed-device-screen.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,804] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/execution-manager.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,806] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/execution-overlay.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,810] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/modules/TestCaseManager.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,811] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/action-description.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,815] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/multi-step-action.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,815] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/guide/faq_guide.html HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,822] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/hook-action.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,822] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/repeat-steps-action.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,825] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/modules/fallback-locators.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,826] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/modules/tap-fallback-manager.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,831] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /static/js/main.js?v=1752228309 HTTP/1.1" 200 -
[2025-07-11 19:15:09,832] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/random-data-generator.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,834] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/test_suites.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,838] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/settings.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,840] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/environment-variables.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,843] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/test-case-modification.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,848] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/js/import-export.js HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-11 19:15:09,884] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:09,886] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/environments HTTP/1.1" 200 -
[2025-07-11 19:15:09,894] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/random_data/generators HTTP/1.1" 200 -
[2025-07-11 19:15:09,899] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:09,909] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:15:09,916] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/environment_variables HTTP/1.1" 200 -
[2025-07-11 19:15:09,924] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/settings HTTP/1.1" 200 -
[2025-07-11 19:15:09,929] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "[36mGET /static/img/favicon.ico HTTP/1.1[0m" 304 -
[2025-07-11 19:15:09,938] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:15:09,947] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/environments/current HTTP/1.1" 200 -
[2025-07-11 19:15:09,953] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/test_suites/list HTTP/1.1" 200 -
[2025-07-11 19:15:09,962] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:15:09,974] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:15:09,978] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/environments/5/variables HTTP/1.1" 200 -
[2025-07-11 19:15:09,987] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:15:09,994] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:09] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:15:10,027] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:10] "GET /api/test_cases/action_types HTTP/1.1" 200 -
[2025-07-11 19:15:10,034] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:10] "GET /api/tools/scan-databases HTTP/1.1" 200 -
[2025-07-11 19:15:10,077] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:10] "GET /api/test_cases/locator_types HTTP/1.1" 200 -
[2025-07-11 19:15:10,080] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:10] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-11 19:15:10,092] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:10] "GET /api/recording/list HTTP/1.1" 200 -
[2025-07-11 19:15:10,123] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:10] "GET /api/test_cases_for_multi_step HTTP/1.1" 200 -
[2025-07-11 19:15:11,663] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-11 19:15:11,669] INFO in appium_device_controller: Appium server is running and ready
[2025-07-11 19:15:11,669] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-11 19:15:11,669] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:11] "GET /api/devices HTTP/1.1" 200 -
[2025-07-11 19:15:14,881] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:14,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:14,885] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:14,886] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:15,245] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8100
[2025-07-11 19:15:15,250] INFO in appium_device_controller: Appium server is running and ready
[2025-07-11 19:15:15,250] INFO in appium_device_controller: Appium server is already running and responsive
[2025-07-11 19:15:15,250] INFO in appium_device_controller: Connecting to device: 00008120-00186C801E13C01E with options: None, platform hint: iOS
[2025-07-11 19:15:15,250] INFO in appium_device_controller: Connection attempt 1/3
[2025-07-11 19:15:15,250] INFO in appium_device_controller: Using provided platform hint: iOS
[2025-07-11 19:15:15,250] INFO in appium_device_controller: Using custom WebDriverAgent URL: http://localhost:8100
[2025-07-11 19:15:15,250] INFO in appium_device_controller: Desired capabilities: {'platformName': 'iOS', 'deviceName': '00008120-00186C801E13C01E', 'udid': '00008120-00186C801E13C01E', 'newCommandTimeout': 300, 'noReset': True, 'automationName': 'XCUITest', 'xcodeOrgId': '', 'xcodeSigningId': 'iPhone Developer', 'webDriverAgentUrl': 'http://localhost:8100', 'showIOSLog': True}
[2025-07-11 19:15:15,251] INFO in appium_device_controller: Connecting to Appium server with options: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True}
[2025-07-11 19:15:15,251] INFO in appium_device_controller: Connecting to iOS device via WebDriverAgent
[2025-07-11 19:15:15,270] INFO in appium_device_controller: Device 00008120-00186C801E13C01E is listed and trusted
[2025-07-11 19:15:15,272] INFO in appium_device_controller: Found port 8100 for device 00008120-00186C801E13C01E in wda_ports.txt
[2025-07-11 19:15:15,272] INFO in appium_device_controller: Using WebDriverAgent URL: http://localhost:8100 for device 00008120-00186C801E13C01E
[2025-07-11 19:15:19,881] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:19,882] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:19,885] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:19,886] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:24,880] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:24,881] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:24,884] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:24,885] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:25,276] INFO in appium_device_controller: WebDriverAgent not detected at http://localhost:8100, will try to start port forwarding: HTTPConnectionPool(host='localhost', port=8100): Read timed out. (read timeout=10)
[2025-07-11 19:15:25,278] INFO in appium_device_controller: Port 8100 is already in use, checking if WebDriverAgent is responding...
[2025-07-11 19:15:29,880] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:29,881] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:29,884] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:29,885] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:30,280] WARNING in appium_device_controller: Port 8100 is in use but WebDriverAgent is not responding: HTTPConnectionPool(host='localhost', port=8100): Read timed out. (read timeout=5). Will restart.
[2025-07-11 19:15:30,437] INFO in appium_device_controller: Killed process 12748 using port 8100
[2025-07-11 19:15:31,576] INFO in appium_device_controller: Using tidevice for port forwarding: 8100 -> 8100
[2025-07-11 19:15:33,588] INFO in appium_device_controller: tidevice port forwarding started successfully
[2025-07-11 19:15:33,589] INFO in appium_device_controller: Checking WebDriverAgent connection (attempt 1/5)...
[2025-07-11 19:15:33,599] INFO in appium_device_controller: WebDriverAgent is running at http://localhost:8100
[2025-07-11 19:15:33,599] INFO in appium_device_controller: WebDriverAgent status: {'value': {'build': {'version': '9.5.0', 'time': 'Jun  8 2025 18:35:21', 'productBundleIdentifier': 'com.facebook.WebDriverAgentRunner'}, 'os': {'testmanagerdVersion': 65535, 'name': 'iOS', 'sdkVersion': '18.4', 'version': '18.5'}, 'device': 'iphone', 'ios': {'ip': '*************'}, 'message': 'WebDriverAgent is ready to accept commands', 'state': 'success', 'ready': True}, 'sessionId': '58E2EDFC-5C85-4543-A080-9FC11E92410C'}
[2025-07-11 19:15:33,603] INFO in appium_device_controller: Appium server is already running
[2025-07-11 19:15:33,603] INFO in appium_device_controller: iOS connection attempt 1/3
[2025-07-11 19:15:33,603] INFO in appium_device_controller: Using capabilities: {'platformName': 'iOS', 'appium:deviceName': '00008120-00186C801E13C01E', 'appium:udid': '00008120-00186C801E13C01E', 'appium:newCommandTimeout': 300, 'appium:noReset': True, 'appium:automationName': 'XCUITest', 'appium:xcodeOrgId': '', 'appium:xcodeSigningId': 'iPhone Developer', 'appium:webDriverAgentUrl': 'http://localhost:8100', 'appium:showIOSLog': True, 'webDriverAgentUrl': 'http://localhost:8100'}
[2025-07-11 19:15:33,607] INFO in appium_device_controller: Appium server status before connection: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': 'fa6f4019c27ea127f9cb1713462b771102c70286', 'built': '2025-07-10 06:23:34 +1000'}}}
[2025-07-11 19:15:33,607] INFO in appium_device_controller: Connecting to Appium server at http://127.0.0.1:4723/wd/hub
[2025-07-11 19:15:34,400] INFO in appium_device_controller: Wrapping driver with Healenium self-healing capabilities
[2025-07-11 19:15:34,404] INFO in appium_device_controller: Driver successfully wrapped with Healenium
[2025-07-11 19:15:34,404] INFO in appium_device_controller: Successfully connected to iOS device
[2025-07-11 19:15:34,404] INFO in appium_device_controller: Connected with session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:15:34,404] INFO in appium_device_controller: Connection verified with capabilities: iOS
[2025-07-11 19:15:34,404] INFO in appium_device_controller: Initializing platform helpers for iOS
[2025-07-11 19:15:34,404] INFO in appium_device_controller: Getting device dimensions
[2025-07-11 19:15:34,881] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:34,882] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:34,885] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:34,886] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:35,601] INFO in appium_device_controller: Got device dimensions from Appium: 393x852
[2025-07-11 19:15:35,601] INFO in appium_device_controller: Device dimensions: (393, 852)
[2025-07-11 19:15:35,606] WARNING in appium_device_controller: Failed to initialize ImageMatcher: No module named 'app.utils'; 'app' is not a package
[2025-07-11 19:15:35,606] INFO in appium_device_controller: Initializing iOS-specific helpers
[2025-07-11 19:15:35,606] INFO in appium_device_controller: Setting up iOS predicate string and class chain support
[2025-07-11 19:15:35,606] INFO in appium_device_controller: iOS version: 18.0
[2025-07-11 19:15:35,607] INFO in appium_device_controller: Using modern keyboard handling for iOS 15+
[2025-07-11 19:15:35,607] INFO in appium_device_controller: Platform helpers initialization completed
[2025-07-11 19:15:35,607] INFO in appium_device_controller: Successfully connected to device on attempt 1
[2025-07-11 19:15:35,607] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-11 19:15:35,614] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-11 19:15:35,614] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-11 19:15:35,615] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-11 19:15:35,616] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-11 19:15:35,616] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-11 19:15:35,617] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-11 19:15:35,618] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-11 19:15:35,619] INFO in action_factory: Registered action handler for 'wait'
[2025-07-11 19:15:35,620] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-11 19:15:35,620] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-11 19:15:35,621] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-11 19:15:35,622] INFO in action_factory: Registered action handler for 'text'
[2025-07-11 19:15:35,634] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-07-11 19:15:35,636] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-11 19:15:35,636] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-11 19:15:35,640] ERROR in action_factory: Error loading action handler from input_text_action: invalid syntax (input_text_action.py, line 226)
[2025-07-11 19:15:35,642] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
[2025-07-11 19:15:35,642] INFO in global_values_db: Global values database initialized successfully
[2025-07-11 19:15:35,643] INFO in global_values_db: Using global values from config.py
[2025-07-11 19:15:35,643] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-07-11 19:15:35,645] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-11 19:15:35,646] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-11 19:15:35,646] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-11 19:15:35,647] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-07-11 19:15:35,649] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-11 19:15:35,650] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-11 19:15:35,651] INFO in action_factory: Registered action handler for 'tap'
[2025-07-11 19:15:35,651] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-11 19:15:35,651] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-11 19:15:35,655] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-11 19:15:35,655] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-11 19:15:35,657] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-11 19:15:35,658] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-11 19:15:35,658] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-11 19:15:35,659] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-11 19:15:35,668] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-07-11 19:15:35,668] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-11 19:15:35,668] INFO in action_factory: Registered action handler for 'info'
[2025-07-11 19:15:35,669] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-11 19:15:35,670] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-11 19:15:35,670] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-11 19:15:35,671] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-11 19:15:35,671] INFO in action_factory: Registered action handler for 'exists'
[2025-07-11 19:15:35,672] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-11 19:15:35,673] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-11 19:15:35,674] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-11 19:15:35,674] INFO in action_factory: Registered action handler for 'test'
[2025-07-11 19:15:35,675] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-11 19:15:35,676] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-11 19:15:35,676] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-11 19:15:35,677] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'text': TextAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-11 19:15:35,677] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'test': TestAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-11 19:15:35,678] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-11 19:15:35,678] INFO in appium_device_controller: Initializing Airtest connection for device: 00008120-00186C801E13C01E...
[2025-07-11 19:15:35,680] INFO in appium_device_controller: Connecting to iOS device with WebDriverAgent at http://localhost:8100
[2025-07-11 19:15:35,687] INFO in ios_device: Initialized MinimalIOSDevice for 00008120-00186C801E13C01E with WDA at http://localhost:8100
[2025-07-11 19:15:35,691] ERROR in appium_device_controller: Failed to get screen resolution from iOS device
[2025-07-11 19:15:35,692] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-11 19:15:35,692] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-11 19:15:35,692] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-11 19:15:36,651] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-11 19:15:36,651] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-11 19:15:37,843] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:37] "POST /api/device/connect HTTP/1.1" 200 -
[2025-07-11 19:15:38,857] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-11 19:15:38,857] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-11 19:15:39,832] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-11 19:15:39,832] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-07-11 19:15:39,833] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:39] "GET /screenshot?deviceId=00008120-00186C801E13C01E&clientSessionId=client_1752225309878_oxs3be109_1752216160962_qoxqmgcjy&t=1752225338854 HTTP/1.1" 200 -
[2025-07-11 19:15:39,878] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:39,879] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:39,882] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:39,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:44,881] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:44,882] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:44,884] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:44,885] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:44] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:49,881] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:49,882] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:49,885] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:49,885] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:49] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:54,880] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:54,881] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:54,884] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:54,885] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:54] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:59,880] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:59,881] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:15:59,884] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:15:59,885] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:15:59] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:04,880] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:04,881] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:04,884] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:04,885] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:04] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:07,858] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:16:09,041] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:16:09,041] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:09] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-11 19:16:09,879] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:09,880] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:09,882] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:09,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:09] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:14,881] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:14,882] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:14] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:19,882] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:19,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:19] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:20,742] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:20] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:16:24,882] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:24,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:24] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:29,882] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:29,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:29] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:30,248] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:30] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:16:34,882] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:34,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:34] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:37,859] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:16:39,066] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:16:39,067] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:39] "GET /api/session/health HTTP/1.1" 200 -
[2025-07-11 19:16:39,882] INFO in directory_utils: Using reports directory from database: /Users/<USER>/Documents/automation-tool/reports
[2025-07-11 19:16:39,883] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:39] "GET /api/reports/latest HTTP/1.1" 200 -
[2025-07-11 19:16:39,949] INFO in _internal: 127.0.0.1 - - [11/Jul/2025 19:16:39] "GET /api/reference_images HTTP/1.1" 200 -
[2025-07-11 19:16:42,187] INFO in player: Executing action: {'type': 'ifThenSteps', 'timestamp': 1752225399944, 'condition_type': 'exists', 'condition': {'locator_type': 'image', 'locator_value': 'empty-bag-ios.png', 'timeout': 20}, 'then_action': {'type': 'tap', 'x': 0, 'y': 0}}
[2025-07-11 19:16:42,187] INFO in player: DEBUG: Current test index at start of execute_action: 0
[2025-07-11 19:16:42,187] INFO in player: DEBUG: Player instance ID: **********, has current_test_idx: True
[2025-07-11 19:16:42,187] INFO in player: DEBUG: Player's current_test_idx value: 0
[2025-07-11 19:16:42,187] INFO in player: DEBUG: Before tracking - current_test_idx.value: 0
[2025-07-11 19:16:42,187] INFO in player: DEBUG: Using local_test_idx: 0 for tracking
[2025-07-11 19:16:42,187] INFO in player: ========== PLAYER EXECUTING ACTION WITH TEST_IDX: 0 ==========
[2025-07-11 19:16:42,187] INFO in player: ========== ACTION TYPE: ifThenSteps ==========
[2025-07-11 19:16:42,188] INFO in player: ========== ACTION ID:  ==========
[2025-07-11 19:16:42,188] INFO in player: ========== GLOBAL CURRENT_TEST_IDX: 0 ==========
[2025-07-11 19:16:42,188] INFO in player: ========== PLAYER CURRENT_TEST_IDX: 0 ==========
[2025-07-11 19:16:42,206] INFO in player: Tracked execution in database: test_idx=0, step_idx=0, action_type=ifThenSteps, action_id=
[2025-07-11 19:16:42,207] INFO in player: Skipping device connection verification for better performance
[2025-07-11 19:16:42,207] INFO in action_factory: Registered basic actions: tap, wait
[2025-07-11 19:16:42,207] INFO in action_factory: Special case: Registering tap_if_image_exists_action.py as 'tapIfImageExists'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'tapIfImageExists'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'multiStep'
[2025-07-11 19:16:42,207] INFO in action_factory: Special case: Registering cleanup_steps_action.py as 'cleanupSteps'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'cleanupSteps'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'swipe'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'getParam'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'wait'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'terminateApp'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'doubleClickImage'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'uninstallApp'
[2025-07-11 19:16:42,207] INFO in action_factory: Registered action handler for 'text'
[2025-07-11 19:16:42,208] ERROR in action_factory: Failed to import module for tap_if_text_exists_action
[2025-07-11 19:16:42,208] INFO in action_factory: Registered action handler for 'waitTill'
[2025-07-11 19:16:42,208] INFO in action_factory: Registered action handler for 'hookAction'
[2025-07-11 19:16:42,211] ERROR in action_factory: Error loading action handler from input_text_action: invalid syntax (input_text_action.py, line 226)
[2025-07-11 19:16:42,211] INFO in action_factory: Registered action handler for 'setParam'
[2025-07-11 19:16:42,211] INFO in action_factory: Special case: Registering repeat_steps_action.py as 'repeatSteps'
[2025-07-11 19:16:42,211] INFO in action_factory: Registered action handler for 'repeatSteps'
[2025-07-11 19:16:42,211] INFO in action_factory: Registered action handler for 'iosFunctions'
[2025-07-11 19:16:42,211] INFO in action_factory: Registered action handler for 'swipeTillVisible'
[2025-07-11 19:16:42,211] INFO in action_factory: Registered action handler for 'clickImage'
[2025-07-11 19:16:42,211] INFO in action_factory: Registered action handler for 'tap'
[2025-07-11 19:16:42,211] INFO in action_factory: Special case: Registering take_screenshot_action.py as 'takeScreenshot'
[2025-07-11 19:16:42,211] INFO in action_factory: Registered action handler for 'takeScreenshot'
[2025-07-11 19:16:42,211] INFO in action_factory: Special case: Registering tap_if_locator_exists_action.py as 'tapIfLocatorExists'
[2025-07-11 19:16:42,211] INFO in action_factory: Registered action handler for 'tapIfLocatorExists'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'tapAndType'
[2025-07-11 19:16:42,212] INFO in action_factory: Special case: Registering tap_on_text_action.py as 'tapOnText'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'tapOnText'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'launchApp'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'ifThenSteps'
[2025-07-11 19:16:42,212] INFO in action_factory: Special case: Registering info_action.py as 'info'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'info'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'waitElement'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'compareValue'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'deviceBack'
[2025-07-11 19:16:42,212] INFO in action_factory: Special case: Registering check_if_exists_action.py as 'exists'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'exists'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'clickElement'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'randomData'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'getValue'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'test'
[2025-07-11 19:16:42,212] INFO in action_factory: Registered action handler for 'restartApp'
[2025-07-11 19:16:42,213] INFO in action_factory: Special case: Registering double_tap_action.py as 'doubleTap'
[2025-07-11 19:16:42,213] INFO in action_factory: Registered action handler for 'doubleTap'
[2025-07-11 19:16:42,213] INFO in action_factory: Registered action types: ['tap', 'wait', 'tapIfImageExists', 'multiStep', 'cleanupSteps', 'swipe', 'getParam', 'terminateApp', 'doubleClickImage', 'uninstallApp', 'text', 'waitTill', 'hookAction', 'setParam', 'repeatSteps', 'iosFunctions', 'swipeTillVisible', 'clickImage', 'takeScreenshot', 'tapIfLocatorExists', 'tapAndType', 'tapOnText', 'launchApp', 'ifThenSteps', 'info', 'waitElement', 'compareValue', 'deviceBack', 'exists', 'clickElement', 'randomData', 'getValue', 'test', 'restartApp', 'doubleTap']
[2025-07-11 19:16:42,213] INFO in action_factory: Handler for 'tap': TapAction
[2025-07-11 19:16:42,213] INFO in action_factory: Handler for 'wait': WaitAction
[2025-07-11 19:16:42,213] INFO in action_factory: Handler for 'tapIfImageExists': TapIfImageExistsAction
[2025-07-11 19:16:42,213] INFO in action_factory: Handler for 'multiStep': MultiStepAction
[2025-07-11 19:16:42,213] INFO in action_factory: Handler for 'cleanupSteps': CleanupStepsAction
[2025-07-11 19:16:42,213] INFO in action_factory: Handler for 'swipe': SwipeAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'getParam': GetParamAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'terminateApp': TerminateAppAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'doubleClickImage': DoubleClickImageAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'uninstallApp': UninstallAppAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'text': TextAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'waitTill': WaitTillAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'hookAction': HookAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'setParam': SetParamAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'repeatSteps': RepeatStepsAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'iosFunctions': IosFunctionsAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'swipeTillVisible': SwipeTillVisibleAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'clickImage': ClickImageAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'takeScreenshot': TakeScreenshotAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'tapIfLocatorExists': TapIfLocatorExistsAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'tapAndType': TapAndTypeAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'tapOnText': TapOnTextAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'launchApp': LaunchAppAction
[2025-07-11 19:16:42,214] INFO in action_factory: Handler for 'ifThenSteps': IfThenStepsAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'info': InfoAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'waitElement': WaitElementAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'compareValue': CompareValueAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'deviceBack': DeviceBackAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'exists': CheckIfExistsAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'clickElement': ClickElementAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'randomData': RandomDataAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'getValue': GetValueAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'test': TestAction
[2025-07-11 19:16:42,215] INFO in action_factory: Handler for 'restartApp': RestartAppAction
[2025-07-11 19:16:42,216] INFO in action_factory: Handler for 'doubleTap': DoubleTapAction
[2025-07-11 19:16:42,216] INFO in player: Forwarding ifThenSteps action to ActionFactory with params: {'condition_type': 'exists', 'condition': {'locator_type': 'image', 'locator_value': 'empty-bag-ios.png', 'timeout': 20}, 'then_action': {'type': 'tap', 'x': 0, 'y': 0}}
[2025-07-11 19:16:42,216] INFO in action_factory: Requested action type: 'ifThenSteps', Available types: ['cleanupSteps', 'clickElement', 'clickImage', 'compareValue', 'deviceBack', 'doubleClickImage', 'doubleTap', 'exists', 'getParam', 'getValue', 'hookAction', 'ifThenSteps', 'info', 'iosFunctions', 'launchApp', 'multiStep', 'randomData', 'repeatSteps', 'restartApp', 'setParam', 'swipe', 'swipeTillVisible', 'takeScreenshot', 'tap', 'tapAndType', 'tapIfImageExists', 'tapIfLocatorExists', 'tapOnText', 'terminateApp', 'test', 'text', 'uninstallApp', 'wait', 'waitElement', 'waitTill']
[2025-07-11 19:16:42,216] INFO in action_factory: Action parameters before env resolution: {'condition_type': 'exists', 'condition': {'locator_type': 'image', 'locator_value': 'empty-bag-ios.png', 'timeout': 20}, 'then_action': {'type': 'tap', 'x': 0, 'y': 0}}
[2025-07-11 19:16:42,223] WARNING in appium_device_controller: TouchAction not available in this Appium Python Client version - using W3C Actions fallback
[2025-07-11 19:16:42,348] INFO in directory_paths_db: Directory paths and environments database initialized/verified
[2025-07-11 19:16:42,367] INFO in app: Using directories from config.py:
[2025-07-11 19:16:42,367] INFO in app:   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/test_cases
[2025-07-11 19:16:42,368] INFO in app:   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/reference_images
[2025-07-11 19:16:42,368] INFO in app:   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
[2025-07-11 19:16:42,371] INFO in appium_device_controller: Finding image: path=/Users/<USER>/Documents/automation-tool/reference_images/empty-bag-ios.png, threshold=0.8, timeout=20
[2025-07-11 19:16:42,372] INFO in appium_device_controller: Using absolute image path: /Users/<USER>/Documents/automation-tool/reference_images/empty-bag-ios.png
[2025-07-11 19:16:42,372] INFO in appium_device_controller: Using resolved image path: /Users/<USER>/Documents/automation-tool/reference_images/empty-bag-ios.png
[2025-07-11 19:16:42,372] INFO in appium_device_controller: Created template with threshold: 0.8
[2025-07-11 19:16:42,376] WARNING in appium_device_controller: Error during image matching (attempt 1): 'No devices added.'
[2025-07-11 19:16:42,883] WARNING in appium_device_controller: Error during image matching (attempt 2): 'No devices added.'
[2025-07-11 19:16:43,385] WARNING in appium_device_controller: Error during image matching (attempt 3): 'No devices added.'
[2025-07-11 19:16:43,892] WARNING in appium_device_controller: Error during image matching (attempt 4): 'No devices added.'
[2025-07-11 19:16:44,398] WARNING in appium_device_controller: Error during image matching (attempt 5): 'No devices added.'
[2025-07-11 19:16:44,904] WARNING in appium_device_controller: Error during image matching (attempt 6): 'No devices added.'
[2025-07-11 19:16:45,407] WARNING in appium_device_controller: Error during image matching (attempt 7): 'No devices added.'
[2025-07-11 19:16:45,914] WARNING in appium_device_controller: Error during image matching (attempt 8): 'No devices added.'
[2025-07-11 19:16:46,417] WARNING in appium_device_controller: Error during image matching (attempt 9): 'No devices added.'
[2025-07-11 19:16:46,922] WARNING in appium_device_controller: Error during image matching (attempt 10): 'No devices added.'
[2025-07-11 19:16:47,425] WARNING in appium_device_controller: Error during image matching (attempt 11): 'No devices added.'
[2025-07-11 19:16:47,931] WARNING in appium_device_controller: Error during image matching (attempt 12): 'No devices added.'
[2025-07-11 19:16:48,434] WARNING in appium_device_controller: Error during image matching (attempt 13): 'No devices added.'
[2025-07-11 19:16:48,941] WARNING in appium_device_controller: Error during image matching (attempt 14): 'No devices added.'
[2025-07-11 19:16:49,447] WARNING in appium_device_controller: Error during image matching (attempt 15): 'No devices added.'
[2025-07-11 19:16:49,949] WARNING in appium_device_controller: Error during image matching (attempt 16): 'No devices added.'
[2025-07-11 19:16:50,455] WARNING in appium_device_controller: Error during image matching (attempt 17): 'No devices added.'
[2025-07-11 19:16:50,959] WARNING in appium_device_controller: Error during image matching (attempt 18): 'No devices added.'
[2025-07-11 19:16:51,466] WARNING in appium_device_controller: Error during image matching (attempt 19): 'No devices added.'
[2025-07-11 19:16:51,970] WARNING in appium_device_controller: Error during image matching (attempt 20): 'No devices added.'
[2025-07-11 19:16:52,474] WARNING in appium_device_controller: Error during image matching (attempt 21): 'No devices added.'
[2025-07-11 19:16:52,980] WARNING in appium_device_controller: Error during image matching (attempt 22): 'No devices added.'
[2025-07-11 19:16:53,485] WARNING in appium_device_controller: Error during image matching (attempt 23): 'No devices added.'
[2025-07-11 19:16:53,992] WARNING in appium_device_controller: Error during image matching (attempt 24): 'No devices added.'
[2025-07-11 19:16:54,497] WARNING in appium_device_controller: Error during image matching (attempt 25): 'No devices added.'
[2025-07-11 19:16:55,000] WARNING in appium_device_controller: Error during image matching (attempt 26): 'No devices added.'
[2025-07-11 19:16:55,504] WARNING in appium_device_controller: Error during image matching (attempt 27): 'No devices added.'
[2025-07-11 19:16:56,010] WARNING in appium_device_controller: Error during image matching (attempt 28): 'No devices added.'
[2025-07-11 19:16:56,517] WARNING in appium_device_controller: Error during image matching (attempt 29): 'No devices added.'
[2025-07-11 19:16:57,022] WARNING in appium_device_controller: Error during image matching (attempt 30): 'No devices added.'
[2025-07-11 19:16:57,529] WARNING in appium_device_controller: Error during image matching (attempt 31): 'No devices added.'
[2025-07-11 19:16:58,033] WARNING in appium_device_controller: Error during image matching (attempt 32): 'No devices added.'
[2025-07-11 19:16:58,538] WARNING in appium_device_controller: Error during image matching (attempt 33): 'No devices added.'
[2025-07-11 19:16:59,044] WARNING in appium_device_controller: Error during image matching (attempt 34): 'No devices added.'
[2025-07-11 19:16:59,550] WARNING in appium_device_controller: Error during image matching (attempt 35): 'No devices added.'
[2025-07-11 19:17:00,053] WARNING in appium_device_controller: Error during image matching (attempt 36): 'No devices added.'
[2025-07-11 19:17:00,559] WARNING in appium_device_controller: Error during image matching (attempt 37): 'No devices added.'
[2025-07-11 19:17:01,065] WARNING in appium_device_controller: Error during image matching (attempt 38): 'No devices added.'
[2025-07-11 19:17:01,570] WARNING in appium_device_controller: Error during image matching (attempt 39): 'No devices added.'
[2025-07-11 19:17:02,077] WARNING in appium_device_controller: Error during image matching (attempt 40): 'No devices added.'
[2025-07-11 19:17:02,581] INFO in appium_device_controller: Image '/Users/<USER>/Documents/automation-tool/reference_images/empty-bag-ios.png' not found after 40 attempts within timeout of 20 seconds
[2025-07-11 19:17:04,276] INFO in appium_device_controller: Tapping at coordinates: (874, 2381)
[2025-07-11 19:17:04,276] INFO in appium_device_controller: Using mobile: tap for iOS at (874, 2381)
[2025-07-11 19:17:06,807] WARNING in appium_device_controller: No action_id provided for screenshot - this should not happen
[2025-07-11 19:17:06,807] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp/screenshots/placeholder.png (save_debug=False)
[2025-07-11 19:17:06,807] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-11 19:17:07,833] INFO in appium_device_controller: Screenshot with action_id placeholder already exists in database, skipping save
[2025-07-11 19:17:07,833] INFO in appium_device_controller: Generated screenshot URL: /screenshots/placeholder.png
[2025-07-11 19:17:07,844] INFO in appium_device_controller: Taking screenshot to /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/static/screenshots/device_00008120_00186C801E13C01E_latest.png (save_debug=False)
[2025-07-11 19:17:07,844] INFO in appium_device_controller: Taking screenshot using Airtest (attempt 1/3)
[2025-07-11 19:17:07,858] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:17:09,201] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:17:09,905] INFO in appium_device_controller: Screenshot also saved as latest.png in report folder: /Users/<USER>/Documents/automation-tool/reports/suite_execution_070df4ba-ad40-4f7e-86f2-17c949548d1d/screenshots/latest.png
[2025-07-11 19:17:09,905] WARNING in appium_device_controller: Not saving screenshot to database because action_id is missing
[2025-07-11 19:17:09,905] INFO in appium_device_controller: Generated screenshot URL: /screenshots/device_00008120_00186C801E13C01E_latest.png
[2025-07-11 19:17:37,861] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:17:39,049] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===[2025-07-11 19:18:07,862] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:18:09,066] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:18:37,861] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:18:39,029] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:19:07,862] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:19:09,067] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:19:37,862] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:19:39,058] DEBUG in appium_device_controller: Session is responsive (window_size check passed)

=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===[2025-07-11 19:20:07,863] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:20:09,052] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:20:37,865] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:20:39,061] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:21:07,866] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:21:09,029] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:21:37,866] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:21:39,039] DEBUG in appium_device_controller: Session is responsive (window_size check passed)

=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===
=== ABOUT TO CALL getLatestReportUrl() ===
=== getLatestReportUrl() RETURNED: /reports/testsuite_execution_20250711_143732/mainreport.html ===
=== LATEST REPORT ENDPOINT CALLED ===
=== REPORTS DIRECTORY: /Users/<USER>/Documents/automation-tool/reports ===
=== SUCCESSFULLY IMPORTED getLatestReportUrl FUNCTION ===[2025-07-11 19:22:07,870] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:22:09,093] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:22:37,867] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:22:39,051] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
[2025-07-11 19:23:07,869] DEBUG in appium_device_controller: Session ID: d84639c3-c689-4ac2-9e88-a8e4204dd7a2
[2025-07-11 19:23:09,056] DEBUG in appium_device_controller: Session is responsive (window_size check passed)
