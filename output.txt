2025-07-11 17:26:00,838 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-11 17:26:00,839 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-07-11 17:26:00,839 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/test_cases
2025-07-11 17:26:00,840 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/reports
2025-07-11 17:26:00,840 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots
2025-07-11 17:26:00,841 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/reference_images
2025-07-11 17:26:00,841 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/test_suites
2025-07-11 17:26:00,841 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/suites
2025-07-11 17:26:00,842 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings
2025-07-11 17:26:00,842 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp
2025-07-11 17:26:00,843 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-07-11 17:26:00,843 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-07-11 17:26:00,843 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-07-11 17:26:02,924 - __main__ - INFO - Existing processes terminated
