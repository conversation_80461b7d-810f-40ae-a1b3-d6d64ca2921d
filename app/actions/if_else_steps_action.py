from .base_action import BaseAction
import logging
import traceback
import json

class IfElseStepsAction(BaseAction):
    """Handler for if conditional actions"""

    def execute(self, params):
        """
        Execute a conditional action - if condition is true, execute then action

        Args:
            params: Dictionary containing:
                - condition_type: Type of condition (exists, not_exists, visible, contains_text, value_equals, has_attribute, etc.)
                - condition: Condition parameters
                - then_action: Action to execute if condition is true

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        condition_type = params.get('condition_type')
        condition = params.get('condition', {})
        then_action = params.get('then_action', {})

        # Log the conditional action
        self.logger.info(f"Executing If Steps with condition type: {condition_type}")
        self.logger.info(f"Then action: {then_action}")

        # Check condition based on type
        condition_met = False
        found_element_coordinates = None  # Store coordinates of found condition element

        # Log the condition details
        self.logger.info(f"Checking condition of type: {condition_type}")
        self.logger.info(f"Condition parameters: {condition}")
        self.logger.info(f"Then action: {then_action}")

        try:
            if condition_type in ['exists', 'not_exists']:
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                # Get timeout from global settings or use provided value
                default_timeout = self.get_global_timeout()
                timeout = condition.get('timeout', default_timeout)

                if not locator_type or not locator_value:
                    return {"status": "error", "message": "Missing locator parameters for condition"}

                # Check if element exists
                self.logger.info(f"Checking if element {condition_type == 'not_exists' and 'does not exist' or 'exists'} with {locator_type}: {locator_value}, timeout={timeout}s")

                # Use appropriate method based on locator type and EXTRACT COORDINATES
                if locator_type == 'image':
                    # Use the same robust image finding logic as TapIfImageExistsAction
                    element_found = self._find_image_with_fallbacks(locator_value, timeout=timeout)
                    if element_found:
                        # Extract coordinates from image match result
                        found_element_coordinates = element_found
                        self.logger.info(f"Found image at coordinates: {found_element_coordinates}")
                elif locator_type == 'text':
                    # For text locator, use text finding logic and extract coordinates
                    element_found, coordinates = self._find_text_with_coordinates(locator_value, timeout=timeout)
                    if element_found:
                        found_element_coordinates = coordinates
                        self.logger.info(f"Found text at coordinates: {found_element_coordinates}")
                else:
                    # For xpath, accessibility_id, etc., find element and extract coordinates
                    element_found = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                    if element_found:
                        # Extract coordinates from WebDriver element
                        found_element_coordinates = self._extract_element_coordinates(element_found)
                        self.logger.info(f"Found element at coordinates: {found_element_coordinates}")

                # For 'exists', condition is met if element is found
                # For 'not_exists', condition is met if element is NOT found
                if condition_type == 'exists':
                    condition_met = element_found is not None
                    self.logger.info(f"Condition check result: Element exists = {condition_met}")
                else:  # not_exists
                    condition_met = element_found is None
                    found_element_coordinates = None  # No coordinates for not_exists
                    self.logger.info(f"Condition check result: Element does not exist = {condition_met}")

            elif condition_type == 'visible':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value:
                    return {"status": "error", "message": "Missing locator parameters for condition"}

                # Check if element is visible
                self.logger.info(f"Checking if element is visible with {locator_type}: {locator_value}, timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                condition_met = element is not None and element.is_displayed()
                self.logger.info(f"Condition check result: Element visible = {condition_met}")

            elif condition_type == 'contains_text':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                text = condition.get('text')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value or not text:
                    return {"status": "error", "message": "Missing parameters for Contains Text condition"}

                # Check if element contains text
                self.logger.info(f"Checking if element {locator_type}: {locator_value} contains text: '{text}', timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    element_text = element.text
                    condition_met = text in element_text
                    self.logger.info(f"Element text: '{element_text}', contains '{text}' = {condition_met}")
                else:
                    self.logger.info("Element not found for contains_text check")

            elif condition_type == 'value_equals':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                expected_value = condition.get('expected_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value or expected_value is None:
                    return {"status": "error", "message": "Missing parameters for Value Equals condition"}

                # Check if element value equals expected value
                self.logger.info(f"Checking if element {locator_type}: {locator_value} value equals: '{expected_value}', timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    if element.get_attribute('value') is not None:
                        actual_value = element.get_attribute('value')
                    else:
                        actual_value = element.text

                    condition_met = actual_value == expected_value
                    self.logger.info(f"Element value: '{actual_value}', equals '{expected_value}' = {condition_met}")
                else:
                    self.logger.info("Element not found for value_equals check")

            elif condition_type == 'value_contains':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                expected_value = condition.get('expected_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value or not expected_value:
                    return {"status": "error", "message": "Missing parameters for Value Contains condition"}

                # Check if element value contains expected value
                self.logger.info(f"Checking if element {locator_type}: {locator_value} value contains: '{expected_value}', timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    if element.get_attribute('value') is not None:
                        actual_value = element.get_attribute('value')
                    else:
                        actual_value = element.text

                    condition_met = expected_value in actual_value
                    self.logger.info(f"Element value: '{actual_value}', contains '{expected_value}' = {condition_met}")
                else:
                    self.logger.info("Element not found for value_contains check")

            elif condition_type == 'has_attribute':
                locator_type = condition.get('locator_type')
                locator_value = condition.get('locator_value')
                attribute_name = condition.get('attribute_name')
                expected_value = condition.get('attribute_value')
                timeout = condition.get('timeout', 10)

                if not locator_type or not locator_value or not attribute_name:
                    return {"status": "error", "message": "Missing parameters for Has Attribute condition"}

                # Check if element has the specified attribute with the expected value
                self.logger.info(f"Checking if element {locator_type}: {locator_value} has attribute '{attribute_name}'{expected_value and f' with value: {expected_value}' or ''}, timeout={timeout}s")

                element = self.controller.find_element(locator_type, locator_value, timeout=timeout)
                if element is not None:
                    try:
                        actual_value = element.get_attribute(attribute_name)
                        self.logger.info(f"Element attribute '{attribute_name}' value: '{actual_value}'")

                        # If expected_value is provided, check if it matches
                        if expected_value:
                            condition_met = str(actual_value).lower() == str(expected_value).lower()
                            self.logger.info(f"Attribute value matches expected value: {condition_met}")
                        else:
                            # If no expected_value is provided, just check if the attribute exists
                            condition_met = actual_value is not None
                            self.logger.info(f"Attribute exists: {condition_met}")
                    except Exception as e:
                        self.logger.error(f"Error getting attribute: {e}")
                        condition_met = False
                else:
                    self.logger.info("Element not found for has_attribute check")
                    condition_met = False

            elif condition_type == 'screen_contains':
                image = condition.get('image')
                threshold = condition.get('threshold', 0.7)
                timeout = condition.get('timeout', 10)

                if not image:
                    return {"status": "error", "message": "Missing image parameter for Screen Contains condition"}

                # Check if screen contains image
                self.logger.info(f"Checking if screen contains image: {image}, threshold={threshold}, timeout={timeout}s")

                match_result = self.controller.find_image(image, threshold=threshold, timeout=timeout)

                # Validate the match result to ensure it's not None and doesn't contain invalid coordinates
                if match_result is not None:
                    # Import the coordinate validator
                    try:
                        import sys
                        import os
                        current_dir = os.path.dirname(os.path.abspath(__file__))
                        parent_dir = os.path.dirname(current_dir)
                        utils_dir = os.path.join(parent_dir, 'utils')
                        if utils_dir not in sys.path:
                            sys.path.insert(0, utils_dir)
                        from coordinate_validator import validate_coordinates
                    except ImportError:
                        def validate_coordinates(coords):
                            return coords if coords else None

                    # Validate coordinates to prevent infinity or NaN values
                    valid_coords = validate_coordinates(match_result)

                    if valid_coords:
                        condition_met = True
                        self.logger.info(f"Screen contains image at valid position: {valid_coords}")
                    else:
                        condition_met = False
                        self.logger.error(f"Screen contains image but coordinates are invalid: {match_result}")
                else:
                    condition_met = False

                self.logger.info(f"Screen contains image check result = {condition_met}")

            else:
                return {"status": "error", "message": f"Unsupported condition type: {condition_type}"}

        except Exception as e:
            self.logger.error(f"Error checking condition: {e}")
            self.logger.error(traceback.format_exc())
            return {"status": "error", "message": f"Error checking condition: {str(e)}"}

        # Create a new action factory instance
        try:
            from .action_factory import ActionFactory
        except ImportError:
            try:
                from action_factory import ActionFactory
            except ImportError:
                try:
                    from app.actions.action_factory import ActionFactory
                except ImportError:
                    # Last resort - try importing from actions module
                    from actions.action_factory import ActionFactory

        action_factory = ActionFactory(self.controller)

        # If condition is met, execute then_action (if provided)
        if condition_met:
            # Check if a then_action was provided
            if then_action is None or not then_action.get('type'):
                self.logger.info("Condition met, but no Then action was provided (No Action selected)")
                return {"status": "success", "message": "Condition met, no action taken (No Action selected)"}

            self.logger.info(f"Condition met, executing then action: {then_action.get('type')}")

            # COORDINATE EXTRACTION FIX: If we found coordinates from the condition element,
            # modify the then_action to use those coordinates instead of default (0,0)
            try:
                modified_then_action = then_action.copy()

                if found_element_coordinates and then_action.get('type') in ['tap', 'tapOnText', 'tapOnImage']:
                    # Extract x, y coordinates
                    if isinstance(found_element_coordinates, (tuple, list)) and len(found_element_coordinates) >= 2:
                        x, y = found_element_coordinates[0], found_element_coordinates[1]
                        self.logger.info(f"Using extracted coordinates from condition element: ({x}, {y})")

                        # Modify the then_action parameters to use the extracted coordinates
                        if then_action.get('type') == 'tap':
                            modified_then_action['x'] = x
                            modified_then_action['y'] = y
                            self.logger.info(f"Modified tap action to use coordinates: ({x}, {y})")
                        elif then_action.get('type') in ['tapOnText', 'tapOnImage']:
                            # For tapOnText and tapOnImage, we can add coordinate hints
                            # These actions will use their normal logic but can fall back to these coordinates
                            modified_then_action['fallback_x'] = x
                            modified_then_action['fallback_y'] = y
                            self.logger.info(f"Added fallback coordinates to {then_action.get('type')} action: ({x}, {y})")
                    else:
                        self.logger.warning(f"Invalid coordinate format from condition element: {found_element_coordinates}")

                # Execute the then action using the action factory with modified coordinates
                self.logger.info(f"Executing then action with action factory: {modified_then_action.get('type')}")
                self.logger.info(f"Modified then action parameters: {modified_then_action}")
                result = action_factory.execute_action(modified_then_action.get('type'), modified_then_action)
                self.logger.info(f"Then action result: {result}")
                return {
                    "status": "success",
                    "message": f"Condition true, executed action: {result.get('message', 'completed')}"
                }
            except Exception as e:
                self.logger.error(f"Error executing then action: {e}")
                self.logger.error(traceback.format_exc())
                return {"status": "error", "message": f"Error executing then action: {str(e)}"}
        else:
            self.logger.info("Condition not met, no action taken")
            return {"status": "success", "message": "Condition not met"}

    def _find_image_with_fallbacks(self, image_filename, timeout=10, threshold=0.8):
        """
        Find image using multiple fallback methods (same logic as TapIfImageExistsAction)

        Args:
            image_filename (str): The filename of the reference image
            timeout (int): Maximum time to wait for the image to appear in seconds
            threshold (float): Similarity threshold (0.0-1.0)

        Returns:
            tuple or None: Coordinates if image found, None otherwise
        """
        try:
            import os
            import base64

            # Resolve the image path properly (same logic as TapIfImageExistsAction)
            abs_path = image_filename
            if not os.path.exists(image_filename):
                # Try to resolve from reference_images directory
                try:
                    from config import DIRECTORIES
                    reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                    if reference_dir:
                        full_path = os.path.join(reference_dir, os.path.basename(image_filename))
                        if os.path.exists(full_path):
                            abs_path = full_path
                            self.logger.info(f"Resolved image path to: {abs_path}")
                        else:
                            # Try directly in reference_images folder
                            ref_path = os.path.join('reference_images', os.path.basename(image_filename))
                            if os.path.exists(ref_path):
                                abs_path = ref_path
                                self.logger.info(f"Resolved image path to: {abs_path}")
                except (ImportError, Exception) as e:
                    self.logger.warning(f"Could not import config: {e}")
                    # Fallback to default reference_images directory
                    reference_images_dir = os.path.join(os.getcwd(), 'reference_images')
                    fallback_path = os.path.join(reference_images_dir, os.path.basename(image_filename))
                    if os.path.exists(fallback_path):
                        abs_path = fallback_path
                        self.logger.info(f"Resolved image path to fallback: {abs_path}")

            # Final check if the image exists
            if not os.path.exists(abs_path):
                self.logger.error(f"Reference image not found: {image_filename}")
                return None

            # Get absolute path for more reliable loading
            abs_path = os.path.abspath(abs_path)
            self.logger.info(f"Using absolute image path for condition check: {abs_path}")

            # METHOD 1: Try Airtest Template and exists()
            try:
                from airtest.core.api import exists
                from airtest.core.cv import Template
                from airtest.core.error import TargetNotFoundError

                self.logger.info(f"Using Airtest Template and exists() method for condition check")

                # Create template and check if it exists
                template_image = Template(abs_path, threshold=threshold)
                match_pos = exists(template_image)

                if match_pos:
                    self.logger.info(f"Image found using Airtest exists() at position: {match_pos}")
                    return match_pos
                else:
                    self.logger.info(f"Image not found using Airtest exists() method")

            except Exception as e:
                self.logger.warning(f"Airtest method failed for condition check: {e}")
                if "No devices added" in str(e):
                    self.logger.info("Airtest not initialized, trying fallback methods for condition check")

            # METHOD 2: Try controller's find_image method if Airtest failed
            if hasattr(self.controller, 'find_image'):
                try:
                    self.logger.info("Trying controller's find_image method for condition check")
                    match_pos = self.controller.find_image(abs_path, threshold=threshold, timeout=timeout)
                    if match_pos:
                        self.logger.info(f"Image found using controller's find_image at position: {match_pos}")
                        return match_pos
                    else:
                        self.logger.info("Image not found using controller's find_image method")
                except Exception as e:
                    self.logger.warning(f"Controller's find_image method failed for condition check: {e}")

            # METHOD 3: Try OpenCV directly for image recognition
            try:
                self.logger.info(f"Trying OpenCV directly for image recognition in condition check: {abs_path}")
                import cv2
                import numpy as np
                from PIL import Image
                import io

                # Take a screenshot using Appium
                if hasattr(self.controller, 'driver') and self.controller.driver:
                    # Get screenshot as base64
                    screenshot_base64 = self.controller.driver.get_screenshot_as_base64()
                    screenshot_data = base64.b64decode(screenshot_base64)

                    # Convert to PIL Image first
                    screenshot_pil = Image.open(io.BytesIO(screenshot_data))
                    original_size = screenshot_pil.size

                    # Convert to OpenCV format
                    screenshot_cv = cv2.cvtColor(np.array(screenshot_pil), cv2.COLOR_RGB2BGR)

                    # Load the template image
                    template = cv2.imread(abs_path)
                    if template is not None:
                        # Get template dimensions
                        h, w = template.shape[:2]

                        # Use a lower threshold for OpenCV matching
                        opencv_threshold = max(0.5, threshold - 0.2)
                        self.logger.info(f"Using OpenCV threshold for condition check: {opencv_threshold} (original: {threshold})")

                        # Try template matching
                        result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)

                        self.logger.info(f"OpenCV template matching result for condition check: {max_val}")

                        if max_val >= opencv_threshold:
                            # Match found, calculate center coordinates
                            x = max_loc[0] + w // 2
                            y = max_loc[1] + h // 2

                            self.logger.info(f"Image found at ({x}, {y}) using OpenCV for condition check")
                            return (x, y)
                        else:
                            self.logger.info(f"Image not found with OpenCV for condition check (max_val: {max_val}, threshold: {opencv_threshold})")
                    else:
                        self.logger.error(f"Failed to load template image for condition check: {abs_path}")

            except ImportError as e:
                self.logger.warning(f"OpenCV not available for condition check: {e}")
            except Exception as e:
                self.logger.warning(f"Error using OpenCV for image recognition in condition check: {e}")

            # If all methods failed, return None
            self.logger.info(f"Image not found using any method for condition check: {image_filename}")
            return None

        except Exception as e:
            self.logger.error(f"Error in _find_image_with_fallbacks: {e}")
            return None

    def _find_text_with_coordinates(self, text_to_find, timeout=10):
        """
        Find text on screen and return both element found status and coordinates
        Uses the same logic as TapOnTextAction

        Args:
            text_to_find (str): Text to search for
            timeout (int): Maximum time to wait

        Returns:
            tuple: (element_found, coordinates) where coordinates is (x, y) or None
        """
        try:
            # Try to find the text using WebDriver first
            if hasattr(self.controller, 'driver') and self.controller.driver:
                self.logger.info("Trying to find text using WebDriver for condition check...")

                # Try different XPath strategies to find the text
                xpath_strategies = [
                    f"//*[contains(text(), '{text_to_find}')]",
                    f"//*[@name='{text_to_find}']",
                    f"//*[@label='{text_to_find}']",
                    f"//*[@value='{text_to_find}']",
                    f"//*[text()='{text_to_find}']",
                    f"//*[@text='{text_to_find}']"
                ]

                for xpath in xpath_strategies:
                    try:
                        self.logger.info(f"Trying XPath for condition check: {xpath}")
                        elements = self.controller.driver.find_elements("xpath", xpath)

                        if elements:
                            element = elements[0]  # Take the first match
                            self.logger.info(f"Found text element using XPath for condition check: {xpath}")

                            # Extract coordinates from the element
                            coordinates = self._extract_element_coordinates(element)
                            if coordinates:
                                self.logger.info(f"Text found at coordinates for condition check: {coordinates}")
                                return True, coordinates

                    except Exception as e:
                        self.logger.debug(f"XPath {xpath} failed for condition check: {e}")
                        continue

            # If WebDriver didn't find the element, try Airtest text recognition
            self.logger.info("WebDriver didn't find the text for condition check, trying Airtest text recognition...")

            try:
                from airtest.core.api import text

                # Use Airtest's text function to find the text
                self.logger.info(f"Using Airtest text() to find for condition check: '{text_to_find}'")
                result = text(text_to_find)

                if result:
                    self.logger.info(f"Airtest found text at for condition check: {result}")
                    return True, result
                else:
                    self.logger.info("Airtest text() did not find the text for condition check")

            except Exception as e:
                self.logger.warning(f"Airtest text recognition failed for condition check: {e}")

            # If both methods failed, return not found
            self.logger.info(f"Text '{text_to_find}' not found using any method for condition check")
            return False, None

        except Exception as e:
            self.logger.error(f"Error in _find_text_with_coordinates: {e}")
            return False, None

    def _extract_element_coordinates(self, element):
        """
        Extract center coordinates from a WebDriver element

        Args:
            element: WebDriver element

        Returns:
            tuple: (x, y) coordinates or None if extraction fails
        """
        try:
            # Get element location and size
            location = element.location
            size = element.size

            # Calculate center coordinates
            center_x = location['x'] + size['width'] // 2
            center_y = location['y'] + size['height'] // 2

            self.logger.info(f"Extracted element coordinates: location={location}, size={size}, center=({center_x}, {center_y})")
            return (center_x, center_y)

        except Exception as e:
            self.logger.warning(f"Failed to extract element coordinates: {e}")
            return None