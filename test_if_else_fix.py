#!/usr/bin/env python3
"""
Test script to verify if-else conditional logic fix
"""

import json
import sys
import os

# Add the app directories to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app_android'))

def test_ios_if_else_action():
    """Test iOS if-else action execution"""
    print("Testing iOS if-else action...")

    try:
        # Test the logic by checking if the file contains the else_action handling
        ios_file_path = os.path.join(os.path.dirname(__file__), 'app', 'actions', 'if_then_steps_action.py')

        if not os.path.exists(ios_file_path):
            print("❌ iOS if-else action file not found")
            return False

        with open(ios_file_path, 'r') as f:
            content = f.read()

        # Check for key indicators that else_action is handled
        checks = [
            'else_action = params.get(\'else_action\', {})',
            'Condition not met, executing else action',
            'execute_action(modified_else_action.get(\'type\')',
            'Condition false, executed else action'
        ]

        missing_checks = []
        for check in checks:
            if check not in content:
                missing_checks.append(check)

        if missing_checks:
            print(f"❌ iOS if-else logic missing: {missing_checks}")
            return False
        else:
            print("✅ iOS if-else logic implementation verified - else action handling present")
            return True

    except Exception as e:
        print(f"❌ iOS test failed with error: {e}")
        return False

def test_android_if_else_action():
    """Test Android if-else action execution"""
    print("Testing Android if-else action...")
    
    try:
        from app_android.actions.if_then_steps_action import IfThenStepsAction
        
        # Create a mock controller
        class MockController:
            def find_element(self, locator_type, locator_value, timeout=10):
                return None  # Simulate element not found
        
        # Create action instance
        action = IfThenStepsAction()
        action.set_controller(MockController())
        
        # Test data with both then and else actions
        test_params = {
            'condition_type': 'exists',
            'condition': {
                'locator_type': 'id',
                'locator_value': 'test_element',
                'timeout': 5
            },
            'then_action': {
                'type': 'tap',
                'x': 100,
                'y': 200
            },
            'else_action': {
                'type': 'tap',
                'x': 300,
                'y': 400
            }
        }
        
        # Execute the action
        result = action.execute(test_params)
        
        print(f"Android Result: {result}")
        
        # Check if else action was executed (since element won't be found)
        if "else action" in result.get('message', ''):
            print("✅ Android if-else logic working correctly - else action executed")
            return True
        else:
            print("❌ Android if-else logic failed - else action not executed")
            return False
            
    except Exception as e:
        print(f"❌ Android test failed with error: {e}")
        return False

def test_action_data_structure():
    """Test that action data structure includes else_action"""
    print("Testing action data structure...")
    
    # Sample if-then action data as it should be saved
    sample_action = {
        "type": "ifThenSteps",
        "action_id": "test123",
        "condition_type": "exists",
        "condition": {
            "locator_type": "image",
            "locator_value": "empty-bag-ios.png",
            "timeout": 25
        },
        "then_action": {
            "type": "tap",
            "x": 0,
            "y": 0
        },
        "else_action": {
            "type": "tap",
            "x": 100,
            "y": 100
        }
    }
    
    # Verify structure
    required_fields = ['type', 'condition_type', 'condition', 'then_action', 'else_action']
    missing_fields = [field for field in required_fields if field not in sample_action]
    
    if missing_fields:
        print(f"❌ Missing required fields: {missing_fields}")
        return False
    
    # Verify else_action has type
    if not sample_action['else_action'].get('type'):
        print("❌ else_action missing type field")
        return False
    
    print("✅ Action data structure is correct")
    return True

if __name__ == "__main__":
    print("=" * 50)
    print("Testing if-else conditional logic fix")
    print("=" * 50)
    
    results = []
    
    # Test data structure
    results.append(test_action_data_structure())
    
    # Test iOS implementation
    results.append(test_ios_if_else_action())
    
    # Test Android implementation  
    results.append(test_android_if_else_action())
    
    print("\n" + "=" * 50)
    print("Test Results Summary")
    print("=" * 50)
    
    if all(results):
        print("✅ All tests passed! If-else logic fix is working correctly.")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the implementation.")
        sys.exit(1)
